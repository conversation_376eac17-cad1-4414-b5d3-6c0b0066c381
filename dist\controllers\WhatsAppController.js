"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppController = void 0;
const environment_1 = require("@config/environment");
class WhatsAppController {
    constructor() {
        this.verifyWebhook = (req, res) => {
            try {
                const { 'hub.mode': mode, 'hub.challenge': challenge, 'hub.verify_token': token } = req.query;
                if (mode === 'subscribe' && token === environment_1.config.whatsapp.verifyToken) {
                    console.log('WEBHOOK VERIFIED');
                    res.status(200).send(challenge);
                }
                else {
                    console.log('Failed verification. Make sure the verify tokens match.');
                    res.status(403).end();
                }
            }
            catch (error) {
                console.error('Error verifying webhook:', error);
                res.status(500).json({
                    success: false,
                    message: 'Internal server error during webhook verification',
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        };
        this.handleWebhook = (req, res) => {
            try {
                console.log('Received webhook:', JSON.stringify(req.body, null, 2));
                res.status(200).json({
                    success: true,
                    message: 'Webhook received successfully'
                });
            }
            catch (error) {
                console.error('Error handling webhook:', error);
                res.status(500).json({
                    success: false,
                    message: 'Internal server error during webhook handling',
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        };
    }
}
exports.WhatsAppController = WhatsAppController;
//# sourceMappingURL=WhatsAppController.js.map