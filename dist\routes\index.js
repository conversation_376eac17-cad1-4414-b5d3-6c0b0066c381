"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.routes = exports.Routes = void 0;
const express_1 = require("express");
const whatsapp_1 = require("./whatsapp");
class Routes {
    constructor() {
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/health', (_req, res) => {
            res.status(200).json({
                success: true,
                message: 'API is healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env['NODE_ENV'] || 'development'
            });
        });
        this.router.get('/', (_req, res) => {
            res.status(200).json({
                success: true,
                message: 'Welcome to Cravin Concierge API',
                version: '1.0.0',
                timestamp: new Date().toISOString(),
                endpoints: {
                    health: '/api/health',
                    whatsapp: '/api/whatsapp/webhook'
                }
            });
        });
        this.router.use('/whatsapp', whatsapp_1.whatsAppRoutes);
    }
}
exports.Routes = Routes;
exports.routes = new Routes().router;
//# sourceMappingURL=index.js.map