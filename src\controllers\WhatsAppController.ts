import { Request, Response } from 'express';
import { config } from '@config/environment';

/**
 * WhatsApp Controller
 * Handles WhatsApp webhook verification and message processing
 */
export class WhatsAppController {
  /**
   * Verify WhatsApp webhook
   * GET /api/whatsapp/webhook
   */
  public verifyWebhook = (req: Request, res: Response): void => {
    try {
      const { 'hub.mode': mode, 'hub.challenge': challenge, 'hub.verify_token': token } = req.query;

      // Check if mode and token are valid
      if (mode === 'subscribe' && token === config.whatsapp.verifyToken) {
        console.log('WEBHOOK VERIFIED');
        res.status(200).send(challenge);
      } else {
        console.log('Failed verification. Make sure the verify tokens match.');
        res.status(403).end();
      }
    } catch (error) {
      console.error('Error verifying webhook:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during webhook verification',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Handle incoming WhatsApp messages
   * POST /api/whatsapp/webhook
   */
  public handleWebhook = (req: Request, res: Response): void => {
    try {
      // TODO: Implement message handling logic here
      console.log('Received webhook:', JSON.stringify(req.body, null, 2));
      
      res.status(200).json({
        success: true,
        message: 'Webhook received successfully'
      });
    } catch (error) {
      console.error('Error handling webhook:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during webhook handling',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}
