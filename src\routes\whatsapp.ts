import { Router } from 'express';
import { WhatsAppController } from '@controllers/WhatsAppController';

/**
 * WhatsApp routes configuration
 */
export class WhatsAppRoutes {
  public router: Router;
  private whatsAppController: WhatsAppController;

  constructor() {
    this.router = Router();
    this.whatsAppController = new WhatsAppController();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // GET /api/whatsapp/webhook - Webhook verification
    this.router.get('/webhook', this.whatsAppController.verifyWebhook);

    // POST /api/whatsapp/webhook - Handle incoming messages
    this.router.post('/webhook', this.whatsAppController.handleWebhook);
  }
}

// Export router instance
export const whatsAppRoutes = new WhatsAppRoutes().router;
